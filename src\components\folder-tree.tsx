'use client';

import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import {
  Folder,
  FolderOpen,
  File,
  Plus,
  Edit,
  Trash2,
  ChevronDown,
  MoreHorizontal
} from 'lucide-react';
import { cn } from '@/lib/utils';

export interface FolderItem {
  id: string;
  name: string;
  type: 'folder' | 'file';
  parentId?: string;
  children?: FolderItem[];
  size?: string;
  date?: string;
  fileType?: string;
}

interface FolderTreeProps {
  items: FolderItem[];
  onItemClick: (item: FolderItem) => void;
  onCreateFolder: (name: string, parentId?: string) => void;
  onRenameItem: (itemId: string, newName: string) => void;
  onDeleteItem: (itemId: string) => void;
  selectedItemId?: string;
  className?: string;
}

export function FolderTree({
  items,
  onItemClick,
  onCreateFolder,
  onRenameItem,
  onDeleteItem,
  selectedItemId,
  className
}: FolderTreeProps) {
  const [isCreateDialogOpen, setIsCreateDialogOpen] = useState(false);
  const [isRenameDialogOpen, setIsRenameDialogOpen] = useState(false);
  const [newFolderName, setNewFolderName] = useState('');
  const [renameItemId, setRenameItemId] = useState<string | null>(null);
  const [renameValue, setRenameValue] = useState('');
  const [contextMenuParentId, setContextMenuParentId] = useState<string | null>(null);

  const handleCreateFolder = () => {
    if (newFolderName.trim()) {
      onCreateFolder(newFolderName.trim(), contextMenuParentId || undefined);
      setNewFolderName('');
      setIsCreateDialogOpen(false);
      setContextMenuParentId(null);
    }
  };

  const handleRename = () => {
    if (renameValue.trim() && renameItemId) {
      onRenameItem(renameItemId, renameValue.trim());
      setRenameValue('');
      setRenameItemId(null);
      setIsRenameDialogOpen(false);
    }
  };

  const openCreateDialog = (parentId: string | null = null) => {
    setContextMenuParentId(parentId);
    setIsCreateDialogOpen(true);
  };

  const openRenameDialog = (item: FolderItem) => {
    setRenameItemId(item.id);
    setRenameValue(item.name);
    setIsRenameDialogOpen(true);
  };

  const renderItem = (item: FolderItem, level: number = 0) => {
    const isSelected = selectedItemId === item.id;
    const hasChildren = item.children && item.children.length > 0;

    return (
      <div key={item.id} className="select-none">
        <div
          className={cn(
            "flex items-center py-1.5 px-2 rounded-md cursor-pointer hover:bg-gray-100 group",
            isSelected && "bg-blue-50 text-blue-700",
            "transition-colors"
          )}
          style={{ paddingLeft: `${level * 20 + 8}px` }}
          onClick={() => onItemClick(item)}
        >
          {item.type === 'folder' && (
            <div className="mr-1 p-0.5">
              {hasChildren ? (
                <ChevronDown className="h-3 w-3" />
              ) : (
                <div className="h-3 w-3" />
              )}
            </div>
          )}
          
          <div className="mr-2">
            {item.type === 'folder' ? (
              isExpanded ? (
                <FolderOpen className="h-4 w-4 text-blue-500" />
              ) : (
                <Folder className="h-4 w-4 text-blue-500" />
              )
            ) : (
              <File className="h-4 w-4 text-gray-500" />
            )}
          </div>
          
          <span className="flex-1 text-sm truncate">{item.name}</span>
          
          <div className="flex items-center gap-1 ml-2">
            {item.type === 'folder' && (
              <Button
                variant="ghost"
                size="sm"
                className="h-6 w-6 p-0"
                onClick={(e) => {
                  e.stopPropagation();
                  openCreateDialog(item.id);
                }}
              >
                <Plus className="h-3 w-3" />
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
              onClick={(e) => {
                e.stopPropagation();
                openRenameDialog(item);
              }}
            >
              <Edit className="h-3 w-3" />
            </Button>
            <Button
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
              onClick={(e) => {
                e.stopPropagation();
                if (confirm(`Are you sure you want to delete "${item.name}"?`)) {
                  onDeleteItem(item.id);
                }
              }}
            >
              <Trash2 className="h-3 w-3" />
            </Button>
          </div>
        </div>

        {item.type === 'folder' && hasChildren && (
          <div>
            {item.children!.map((child) => renderItem(child, level + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className={cn("space-y-1", className)}>
      <div className="flex items-center justify-between mb-2">
        <h3 className="text-sm font-medium text-gray-700">Folders</h3>
        <Button
          variant="ghost"
          size="sm"
          className="h-6 w-6 p-0"
          onClick={() => openCreateDialog(null)}
        >
          <Plus className="h-3 w-3" />
        </Button>
      </div>
      
      {items.map((item) => renderItem(item))}

      {/* Create Folder Dialog */}
      <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Create New Folder</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="Folder name"
              value={newFolderName}
              onChange={(e) => setNewFolderName(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleCreateFolder()}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsCreateDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateFolder} disabled={!newFolderName.trim()}>
              Create
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Rename Dialog */}
      <Dialog open={isRenameDialogOpen} onOpenChange={setIsRenameDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Rename Item</DialogTitle>
          </DialogHeader>
          <div className="py-4">
            <Input
              placeholder="New name"
              value={renameValue}
              onChange={(e) => setRenameValue(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && handleRename()}
              autoFocus
            />
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsRenameDialogOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleRename} disabled={!renameValue.trim()}>
              Rename
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
