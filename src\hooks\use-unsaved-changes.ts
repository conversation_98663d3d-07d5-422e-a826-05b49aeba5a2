'use client';

import { useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';

interface UseUnsavedChangesOptions {
  hasUnsavedChanges: boolean;
  onSave?: () => Promise<void> | void;
  message?: string;
}

export function useUnsavedChanges({
  hasUnsavedChanges,
  onSave,
  message = 'You have unsaved changes. Are you sure you want to leave?'
}: UseUnsavedChangesOptions) {
  const router = useRouter();

  // Handle browser beforeunload event (refresh, close tab, etc.)
  useEffect(() => {
    const handleBeforeUnload = (e: BeforeUnloadEvent) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = message;
        return message;
      }
    };

    if (hasUnsavedChanges) {
      window.addEventListener('beforeunload', handleBeforeUnload);
    }

    return () => {
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [hasUnsavedChanges, message]);

  // Handle browser back/forward navigation
  useEffect(() => {
    const handlePopState = (e: PopStateEvent) => {
      if (hasUnsavedChanges) {
        const confirmLeave = window.confirm(message);
        if (!confirmLeave) {
          // Prevent navigation by pushing current state back
          window.history.pushState(null, '', window.location.href);
        }
      }
    };

    if (hasUnsavedChanges) {
      // Push a state to handle back button
      window.history.pushState(null, '', window.location.href);
      window.addEventListener('popstate', handlePopState);
    }

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [hasUnsavedChanges, message]);

  // Provide a function to check for unsaved changes before navigation
  const checkUnsavedChanges = useCallback(() => {
    if (hasUnsavedChanges) {
      return window.confirm(message);
    }
    return true;
  }, [hasUnsavedChanges, message]);

  // Provide a function to save and navigate
  const saveAndNavigate = useCallback(async (path: string) => {
    if (onSave) {
      try {
        await onSave();
        router.push(path);
      } catch (error) {
        console.error('Error saving changes:', error);
      }
    } else {
      router.push(path);
    }
  }, [onSave, router]);

  return {
    checkUnsavedChanges,
    saveAndNavigate,
  };
}
