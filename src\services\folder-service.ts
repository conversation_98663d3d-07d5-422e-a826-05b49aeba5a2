// Client-side service for folder management

export interface Folder {
  id: string;
  name: string;
  type: 'folder';
  parentId?: string;
  children?: Folder[];
  project_id?: string;
  created_at: string;
  updated_at: string;
}

/**
 * Get folders for a specific project and type
 */
export async function getFolders(
  projectId: string, 
  type: 'documents' | 'photos'
): Promise<{ data: Folder[] | null; error: any }> {
  try {
    console.log('Fetching folders for project:', projectId, 'type:', type);

    const response = await fetch(`/api/folders?projectId=${projectId}&type=${type}`);
    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to fetch folders' };
    }

    return result;
  } catch (error) {
    console.error('Error in getFolders:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Create a new folder
 */
export async function createFolder(
  name: string,
  projectId: string,
  type: 'documents' | 'photos',
  parentId?: string
): Promise<{ data: Folder | null; error: any }> {
  try {
    console.log('Creating folder:', name, 'for project:', projectId);

    const response = await fetch('/api/folders', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name,
        projectId,
        type,
        parentId,
      }),
    });

    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to create folder' };
    }

    return result;
  } catch (error) {
    console.error('Error in createFolder:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Delete a folder
 */
export async function deleteFolder(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting folder:', id);

    const response = await fetch(`/api/folders/${id}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!response.ok) {
      return { success: false, error: result.error || 'Failed to delete folder' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deleteFolder:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Rename a folder
 */
export async function renameFolder(
  id: string, 
  name: string
): Promise<{ data: Folder | null; error: any }> {
  try {
    console.log('Renaming folder:', id, 'to:', name);

    const response = await fetch(`/api/folders/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ name }),
    });

    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to rename folder' };
    }

    return result;
  } catch (error) {
    console.error('Error in renameFolder:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Download a document
 */
export async function downloadDocument(id: string, filename: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Downloading document:', id);

    const response = await fetch(`/api/documents/${id}/download`);

    if (!response.ok) {
      const result = await response.json();
      return { success: false, error: result.error || 'Failed to download document' };
    }

    // Create blob and download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in downloadDocument:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Download a photo
 */
export async function downloadPhoto(id: string, filename: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Downloading photo:', id);

    const response = await fetch(`/api/photos/${id}/download`);

    if (!response.ok) {
      const result = await response.json();
      return { success: false, error: result.error || 'Failed to download photo' };
    }

    // Create blob and download
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in downloadPhoto:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Delete a document
 */
export async function deleteDocument(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting document:', id);

    const response = await fetch(`/api/documents?id=${id}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!response.ok) {
      return { success: false, error: result.error || 'Failed to delete document' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deleteDocument:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Delete a photo
 */
export async function deletePhoto(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting photo:', id);

    const response = await fetch(`/api/photos?id=${id}`, {
      method: 'DELETE',
    });

    const result = await response.json();

    if (!response.ok) {
      return { success: false, error: result.error || 'Failed to delete photo' };
    }

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deletePhoto:', error);
    return { success: false, error: error instanceof Error ? error.message : 'Network error' };
  }
}
