// Client-side service that calls API routes

export interface Photo {
  id: string;
  title: string;
  date: string;
  thumbnail: string;
  file_path?: string;
  project_id?: string;
  folder_id?: string;
}

/**
 * Get photos for a specific project or all photos
 */
export async function getPhotos(projectId?: string): Promise<{ data: Photo[] | null; error: any }> {
  try {
    console.log('Fetching photos from API', projectId ? `for project ${projectId}` : '');

    const url = projectId ? `/api/photos?projectId=${projectId}` : '/api/photos';
    const response = await fetch(url);
    const result = await response.json();

    if (!response.ok) {
      return { data: null, error: result.error || 'Failed to fetch photos' };
    }

    return result;
  } catch (error) {
    console.error('Error in getPhotos:', error);
    return { data: null, error: error instanceof Error ? error.message : 'Network error' };
  }
}

/**
 * Upload a photo (for now, just create a database record)
 * In a real implementation, you would save the file to a local directory
 */
export async function uploadPhoto(
  file: File, 
  projectId?: string, 
  folder?: string
): Promise<{ data: Photo | null; error: any }> {
  try {
    console.log('Creating photo record:', file.name);

    // Extract title from filename (without extension)
    const title = file.name.split('.')[0];
    
    // Generate a unique file path
    const fileId = uuidv4();
    const fileExt = file.name.split('.').pop();
    const filePath = `${fileId}.${fileExt}`;

    // Create photo record in database
    const photo = await prisma.photo.create({
      data: {
        title: title,
        file_path: filePath,
        file_size: file.size,
        mime_type: file.type,
        project_id: projectId || null,
        folder: folder || null,
      },
    });

    const photoData: Photo = {
      id: photo.id,
      title: photo.title,
      date: photo.created_at.toISOString().split('T')[0],
      thumbnail: `/uploads/photos/${photo.file_path}`,
      file_path: photo.file_path,
      project_id: photo.project_id,
      folder: photo.folder,
    };

    return { data: photoData, error: null };
  } catch (error) {
    console.error('Error in uploadPhoto:', error);
    return { data: null, error };
  }
}

/**
 * Delete a photo
 */
export async function deletePhoto(id: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Deleting photo:', id);

    await prisma.photo.delete({
      where: {
        id: id,
      },
    });

    return { success: true, error: null };
  } catch (error) {
    console.error('Error in deletePhoto:', error);
    return { success: false, error };
  }
}

/**
 * Create a folder for organizing photos
 */
export async function createFolder(name: string, projectId?: string): Promise<{ success: boolean; error: any }> {
  try {
    console.log('Creating folder:', name);
    
    // For now, folders are just a string field in the photo records
    // In a more complex implementation, you might have a separate folders table
    
    return { success: true, error: null };
  } catch (error) {
    console.error('Error in createFolder:', error);
    return { success: false, error };
  }
}

/**
 * Get photos by folder
 */
export async function getPhotosByFolder(
  folder: string, 
  projectId?: string
): Promise<{ data: Photo[] | null; error: any }> {
  try {
    console.log('Fetching photos by folder:', folder);

    const photos = await prisma.photo.findMany({
      where: {
        folder: folder,
        ...(projectId && { project_id: projectId }),
      },
      orderBy: {
        created_at: 'desc',
      },
    });

    const photosData: Photo[] = photos.map((photo: any) => ({
      id: photo.id,
      title: photo.title,
      date: photo.created_at.toISOString().split('T')[0],
      thumbnail: photo.file_path ? `/uploads/photos/${photo.file_path}` : '',
      file_path: photo.file_path,
      project_id: photo.project_id,
      folder: photo.folder,
    }));

    return { data: photosData, error: null };
  } catch (error) {
    console.error('Error in getPhotosByFolder:', error);
    return { data: null, error };
  }
}
